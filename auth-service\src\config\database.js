const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'atma_db',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  dialect: process.env.DB_DIALECT || 'postgres',
  schema: process.env.DB_SCHEMA || 'auth',
  logging: process.env.NODE_ENV === 'development' ?
    (msg) => logger.debug(msg) : false,
  pool: {
    max: parseInt(process.env.DB_POOL_MAX || '50'),     // Optimized for high concurrency
    min: parseInt(process.env.DB_POOL_MIN || '10'),     // Maintain minimum connections
    acquire: parseInt(process.env.DB_POOL_ACQUIRE || '30000'), // Connection acquisition timeout
    idle: parseInt(process.env.DB_POOL_IDLE || '10000'), // Optimized idle timeout
    evict: parseInt(process.env.DB_POOL_EVICT || '5000'), // Frequent eviction for fresh connections
    handleDisconnects: true,
    validate: true, // Validate connections before use
    retry: {
      max: 3,
      timeout: 5000,
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ENOTFOUND/,
        /ENETUNREACH/,
        /EAI_AGAIN/
      ]
    }
  },
  define: {
    timestamps: true,
    underscored: true,
    schema: process.env.DB_SCHEMA || 'auth'
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(config);

// Test database connection with health monitoring
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection established successfully', {
      host: config.host,
      database: config.database,
      schema: config.schema,
      poolConfig: {
        max: config.pool.max,
        min: config.pool.min,
        acquire: config.pool.acquire,
        idle: config.pool.idle
      }
    });
    return true;
  } catch (error) {
    logger.error('Unable to connect to the database', {
      error: error.message,
      host: config.host,
      database: config.database
    });
    return false;
  }
};

// Get connection pool status
const getPoolStatus = () => {
  const pool = sequelize.connectionManager.pool;
  return {
    size: pool.size,
    available: pool.available,
    using: pool.using,
    waiting: pool.waiting
  };
};

// Monitor connection pool health
const monitorPoolHealth = () => {
  setInterval(() => {
    const poolStatus = getPoolStatus();
    const utilizationRate = (poolStatus.using / config.pool.max) * 100;

    if (utilizationRate > 80) {
      logger.warn('High database pool utilization', {
        ...poolStatus,
        utilizationRate: `${utilizationRate.toFixed(2)}%`,
        maxConnections: config.pool.max
      });
    } else {
      logger.debug('Database pool status', {
        ...poolStatus,
        utilizationRate: `${utilizationRate.toFixed(2)}%`
      });
    }
  }, 30000); // Check every 30 seconds
};

// Initialize database connection but don't throw error
// This allows the service to start even if the database is not available
testConnection().then(connected => {
  if (connected && process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
    monitorPoolHealth();
  }
});

module.exports = sequelize;
module.exports.testConnection = testConnection;
module.exports.getPoolStatus = getPoolStatus;
